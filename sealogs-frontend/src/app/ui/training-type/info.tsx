'use client'

import type React from 'react'

import { redirect, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { TrainingTypeInfoSkeleton } from '../../../components/skeletons'
import { getTrainingTypeByID } from '@/app/lib/actions'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Edit, Plus } from 'lucide-react'
import { cn } from '@/app/lib/utils'
import { P } from '@/components/ui'

// Helper component for displaying data rows
function DetailRow({
    label,
    value,
    className,
}: {
    label: string
    value?: React.ReactNode
    className?: string
}) {
    if (!value && value !== 0) return null
    return (
        <div className={cn('space-y-2', className)}>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-2 md:gap-4">
                <P className="flex items-center justify-end md:text-right">
                    {label}
                </P>
                <div className="md:col-span-3 text-sm">{value}</div>
            </div>
        </div>
    )
}

const TrainingScheduleInfo = ({
    trainingTypeId,
}: {
    trainingTypeId: number
}) => {
    if (trainingTypeId <= 0) {
        redirect('/training-type')
    }
    const router = useRouter()
    const [trainingType, setTrainingType] = useState<any>()

    getTrainingTypeByID(trainingTypeId, setTrainingType)

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <div className="w-full space-y-8">
            {!trainingType ? (
                <Card>
                    <CardContent>
                        <TrainingTypeInfoSkeleton />
                    </CardContent>
                </Card>
            ) : (
                <>
                    {/* Header Card */}
                    <Card>
                        <CardHeader>
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                <div className="space-y-1">
                                    <CardTitle className="text-2xl">
                                        Training Types
                                    </CardTitle>
                                    <CardDescription>
                                        View and manage training type
                                        information
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2 w-full sm:w-auto">
                                    <Button
                                        variant="back"
                                        onClick={() => router.back()}>
                                        Back
                                    </Button>
                                    <Button
                                        variant="outline"
                                        iconLeft={<Edit />}
                                        onClick={() => {
                                            router.push(
                                                `/training-type/edit?id=${trainingTypeId}`,
                                            )
                                        }}>
                                        Edit
                                    </Button>
                                    {permissions &&
                                        hasPermission(
                                            'RECORD_TRAINING',
                                            permissions,
                                        ) && (
                                            <Button
                                                onClick={() =>
                                                    router.push(
                                                        `/crew-training/create?trainingTypeId=${trainingTypeId}`,
                                                    )
                                                }>
                                                Record training
                                            </Button>
                                        )}
                                </div>
                            </div>
                        </CardHeader>
                    </Card>

                    {/* Details Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Training Details</CardTitle>
                            <CardDescription>
                                Complete information about this training type
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <DetailRow
                                label="Nature Of Training"
                                value={
                                    trainingType?.title && (
                                        <span className="font-medium">
                                            {trainingType.title}
                                        </span>
                                    )
                                }
                            />

                            {trainingType?.occursEvery ? (
                                <>
                                    <Separator />
                                    <DetailRow
                                        label="Occurs Every"
                                        value={
                                            <div className="flex items-center gap-2">
                                                <Badge
                                                    variant="secondary"
                                                    className="">
                                                    {trainingType.occursEvery}
                                                </Badge>
                                                <span className="text-muted-foreground">
                                                    {trainingType.occursEvery >
                                                    1
                                                        ? 'days'
                                                        : 'day'}
                                                </span>
                                            </div>
                                        }
                                    />
                                </>
                            ) : null}

                            {trainingType?.mediumWarnWithin ? (
                                <>
                                    <Separator />
                                    <DetailRow
                                        label="Medium Warning Within"
                                        value={
                                            <div className="flex items-center gap-2">
                                                <Badge variant="warning">
                                                    {
                                                        trainingType.mediumWarnWithin
                                                    }
                                                </Badge>
                                                <span className="text-muted-foreground">
                                                    {trainingType.mediumWarnWithin >
                                                    1
                                                        ? 'days'
                                                        : 'day'}
                                                </span>
                                            </div>
                                        }
                                    />
                                </>
                            ) : null}

                            {trainingType?.highWarnWithin ? (
                                <>
                                    <Separator />
                                    <DetailRow
                                        label="High Warning Within"
                                        value={
                                            <div className="flex items-center gap-2">
                                                <Badge
                                                    variant="destructive"
                                                    className="">
                                                    {
                                                        trainingType.highWarnWithin
                                                    }
                                                </Badge>
                                                <span className="text-muted-foreground">
                                                    {trainingType.highWarnWithin >
                                                    1
                                                        ? 'days'
                                                        : 'day'}
                                                </span>
                                            </div>
                                        }
                                    />
                                </>
                            ) : null}

                            {trainingType?.procedure ? (
                                <>
                                    <Separator />
                                    <DetailRow
                                        label="Procedure"
                                        value={
                                            <div className="prose prose-sm max-w-none dark:prose-invert">
                                                <div
                                                    dangerouslySetInnerHTML={{
                                                        __html: trainingType.procedure,
                                                    }}
                                                />
                                            </div>
                                        }
                                    />
                                </>
                            ) : null}

                            {trainingType?.vessels?.nodes?.length > 0 ? (
                                <>
                                    <Separator />
                                    <DetailRow
                                        label="Vessels"
                                        value={
                                            <div className="flex flex-wrap gap-2">
                                                {trainingType.vessels.nodes.map(
                                                    (
                                                        vessel: any,
                                                        index: number,
                                                    ) => (
                                                        <Badge
                                                            key={index}
                                                            type="normal"
                                                            variant="outline">
                                                            {vessel.title}
                                                        </Badge>
                                                    ),
                                                )}
                                            </div>
                                        }
                                    />
                                </>
                            ) : null}
                        </CardContent>
                    </Card>
                </>
            )}
        </div>
    )
}
export default TrainingScheduleInfo
